import cv2
import os
import logging
import subprocess

def extract_frames(video_path, output_folder, total_frames):
    """
    从视频中提取指定数量的帧并保存到输出文件夹

    参数:
    video_path (str): 视频文件的路径
    output_folder (str): 保存提取帧的文件夹
    total_frames (int): 需要提取的帧数

    返回:
    bool: 提取成功返回True，否则返回False
    """
    try:
        # 打开视频文件
        video_capture = cv2.VideoCapture(video_path)
        if not video_capture.isOpened():
            logging.error("无法打开视频文件")
            return False

        # 获取视频的总帧数
        frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))

        # 计算提取帧的间隔
        frame_interval = int(frame_count / total_frames)

        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        count = 0
        frames_extracted = 0

        while video_capture.isOpened():
            ret, frame = video_capture.read()
            if not ret:
                break

            if count % frame_interval == 0:
                frame_filename = os.path.join(output_folder, f"frame_{frames_extracted:04d}.jpg")
                cv2.imwrite(frame_filename, frame)
                frames_extracted += 1

                if frames_extracted >= total_frames:
                    break

            count += 1

    except Exception as e:
        logging.error(f"提取错误: {str(e)}")
        return False
    finally:
        video_capture.release()

    print(f"\n提取完成。{frames_extracted} 帧保存到 {output_folder}")
    return True


if __name__ == "__main__":
    # extract_frames(
    #     video_path=r"images/video.mp4",
    #     output_folder=r"images",
    #     total_frames=100  # 需要提取的帧数
    # )

    # 执行命令
    # subprocess.run(["./metashape", "-r", "auto.py", "-platform", "offscreen"])
    subprocess.run(["./metashape", "-r", "distributed_metashape.py", "-platform", "offscreen"])