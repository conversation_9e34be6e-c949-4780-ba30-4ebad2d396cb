#!/usr/bin/env python3
"""
WSL环境下Metashape GPU配置脚本
专门针对WSL环境优化GPU使用
"""

import Metashape
import os
import subprocess
import time

def check_wsl_gpu_support():
    """检查WSL GPU支持"""
    print("=== WSL GPU支持检测 ===")
    
    # 检查nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ NVIDIA驱动在WSL中可用")
            # 提取GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'RTX' in line or 'GTX' in line or 'Quadro' in line:
                    print(f"  检测到GPU: {line.strip()}")
            return True
        else:
            print("❌ nvidia-smi不可用")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi未找到")
        return False

def configure_wsl_gpu_for_metashape():
    """为WSL环境配置Metashape GPU设置"""
    print("\n=== WSL Metashape GPU配置 ===")
    
    try:
        # 保存原始设置
        original_gpu_mask = Metashape.app.gpu_mask
        original_cpu_enable = Metashape.app.cpu_enable
        
        print(f"原始GPU掩码: {original_gpu_mask}")
        print(f"原始CPU启用: {original_cpu_enable}")
        
        # WSL环境下的推荐设置
        print("\n应用WSL优化设置...")
        
        # 1. 启用所有GPU
        Metashape.app.gpu_mask = 0xFFFFFFFF
        print(f"设置GPU掩码为: {Metashape.app.gpu_mask}")
        
        # 2. 启用CPU（混合模式在WSL中通常表现更好）
        Metashape.app.cpu_enable = True
        print(f"CPU启用状态: {Metashape.app.cpu_enable}")
        
        # 3. 验证设置
        current_gpu_mask = Metashape.app.gpu_mask
        current_cpu_enable = Metashape.app.cpu_enable
        
        if current_gpu_mask > 0:
            print("✓ GPU配置成功")
            return True
        else:
            print("❌ GPU配置失败，GPU掩码仍为0")
            # 恢复原始设置
            Metashape.app.gpu_mask = original_gpu_mask
            Metashape.app.cpu_enable = original_cpu_enable
            return False
            
    except Exception as e:
        print(f"❌ GPU配置异常: {e}")
        return False

def test_wsl_gpu_performance():
    """在WSL环境下测试GPU性能"""
    print("\n=== WSL GPU性能测试 ===")
    
    # 检查测试图片
    test_images_path = "images/"
    if not os.path.exists(test_images_path):
        print("❌ 测试图片目录不存在，创建示例...")
        os.makedirs(test_images_path, exist_ok=True)
        print("请在 images/ 目录中放入一些测试图片")
        return
    
    image_files = [f for f in os.listdir(test_images_path) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if len(image_files) < 3:
        print("❌ 测试图片数量不足（需要至少3张）")
        return
    
    # 创建测试项目
    doc = Metashape.Document()
    chunk = doc.addChunk()
    
    # 使用少量图片进行快速测试
    test_images = image_files[:min(4, len(image_files))]
    chunk.addPhotos([os.path.join(test_images_path, f) for f in test_images])
    
    print(f"使用 {len(test_images)} 张图片进行WSL GPU测试")
    
    try:
        # 预处理步骤
        print("执行图片匹配和对齐...")
        chunk.matchPhotos(downscale=2, generic_preselection=True)
        chunk.alignCameras()
        
        # 测试1: CPU模式
        print("\n--- WSL CPU模式测试 ---")
        Metashape.app.gpu_mask = 0
        Metashape.app.cpu_enable = True
        
        start_time = time.time()
        chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
        cpu_time = time.time() - start_time
        print(f"CPU深度图构建时间: {cpu_time:.2f} 秒")
        
        chunk.clearDepthMaps()
        
        # 测试2: GPU模式
        print("\n--- WSL GPU模式测试 ---")
        Metashape.app.gpu_mask = 0xFFFFFFFF
        Metashape.app.cpu_enable = False
        
        start_time = time.time()
        chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
        gpu_time = time.time() - start_time
        print(f"GPU深度图构建时间: {gpu_time:.2f} 秒")
        
        chunk.clearDepthMaps()
        
        # 测试3: 混合模式（推荐）
        print("\n--- WSL 混合模式测试 ---")
        Metashape.app.gpu_mask = 0xFFFFFFFF
        Metashape.app.cpu_enable = True
        
        start_time = time.time()
        chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
        hybrid_time = time.time() - start_time
        print(f"混合模式深度图构建时间: {hybrid_time:.2f} 秒")
        
        # 性能分析
        print(f"\n=== WSL性能对比 ===")
        print(f"CPU模式:  {cpu_time:.2f} 秒")
        print(f"GPU模式:  {gpu_time:.2f} 秒")
        print(f"混合模式: {hybrid_time:.2f} 秒")
        
        if gpu_time > 0:
            gpu_speedup = cpu_time / gpu_time
            print(f"GPU加速比: {gpu_speedup:.2f}x")
        
        if hybrid_time > 0:
            hybrid_speedup = cpu_time / hybrid_time
            print(f"混合模式加速比: {hybrid_speedup:.2f}x")
        
        # 推荐最佳模式
        times = [('CPU', cpu_time), ('GPU', gpu_time), ('混合', hybrid_time)]
        best_mode = min(times, key=lambda x: x[1])
        print(f"\n推荐模式: {best_mode[0]} ({best_mode[1]:.2f}秒)")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def provide_wsl_optimization_tips():
    """提供WSL优化建议"""
    print("\n" + "="*50)
    print("WSL GPU优化建议")
    print("="*50)
    
    print("""
1. WSL2 GPU支持要求:
   - Windows 11 或 Windows 10 版本 21H2+
   - WSL 2.0+
   - 支持WSL的NVIDIA驱动

2. 性能优化建议:
   - 使用混合模式 (CPU + GPU)
   - 适当降低downscale参数
   - 监控GPU内存使用情况

3. 常见问题解决:
   - 如果GPU不可用，检查Windows端NVIDIA驱动
   - 重启WSL: wsl --shutdown (在Windows PowerShell中)
   - 检查WSL版本: wsl -l -v

4. 环境变量设置 (添加到 ~/.bashrc):
   export CUDA_VISIBLE_DEVICES=0
   export NVIDIA_VISIBLE_DEVICES=all

5. Metashape最佳实践:
   - 大型项目建议使用混合模式
   - 小型项目可以尝试纯GPU模式
   - 监控系统资源使用情况
""")

def main():
    """主函数"""
    print("WSL Metashape GPU 配置工具")
    print("="*40)
    
    # 1. 检查WSL GPU支持
    wsl_gpu_available = check_wsl_gpu_support()
    
    if not wsl_gpu_available:
        print("\n❌ WSL GPU支持不可用")
        provide_wsl_optimization_tips()
        return
    
    # 2. 配置Metashape GPU
    gpu_configured = configure_wsl_gpu_for_metashape()
    
    if not gpu_configured:
        print("\n❌ Metashape GPU配置失败")
        provide_wsl_optimization_tips()
        return
    
    # 3. 性能测试
    print("\n是否进行性能测试？(需要测试图片)")
    test_wsl_gpu_performance()
    
    # 4. 提供优化建议
    provide_wsl_optimization_tips()
    
    print("\n" + "="*40)
    print("WSL GPU配置完成")

if __name__ == "__main__":
    main()
