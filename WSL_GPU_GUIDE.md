# WSL中GPU使用完整指南

## 🎯 概述

在WSL (Windows Subsystem for Linux) 中使用GPU进行Metashape等应用的加速需要特定的配置。本指南将详细说明如何在WSL环境中启用和优化GPU使用。

## 📋 前提条件

### 系统要求
- **Windows 11** 或 **Windows 10 版本 21H2+**
- **WSL 2** (不支持WSL 1)
- **NVIDIA GPU** (支持CUDA)
- **支持WSL的NVIDIA驱动**

### 检查WSL版本
```bash
# 在Windows PowerShell中运行
wsl -l -v
```

如果显示版本1，需要升级到版本2：
```bash
wsl --set-version <发行版名称> 2
```

## 🔧 安装步骤

### 1. Windows端配置

#### 安装支持WSL的NVIDIA驱动
1. 访问 [NVIDIA官网](https://www.nvidia.com/Download/index.aspx)
2. 下载最新的**支持WSL的驱动程序**
3. 安装驱动（会自动支持WSL）

⚠️ **重要**: 不要在WSL内安装NVIDIA驱动！

### 2. WSL端配置

#### 安装CUDA工具包
```bash
# 方法1: 使用apt安装
sudo apt update
sudo apt install nvidia-cuda-toolkit

# 方法2: 安装官方CUDA (推荐)
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-ubuntu2004.pin
sudo mv cuda-ubuntu2004.pin /etc/apt/preferences.d/cuda-repository-pin-600

# 下载CUDA安装包
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb

# 安装
sudo dpkg -i cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
sudo cp /var/cuda-repo-ubuntu2004-11-8-local/cuda-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install cuda
```

#### 设置环境变量
编辑 `~/.bashrc` 文件：
```bash
nano ~/.bashrc
```

添加以下内容：
```bash
# CUDA环境变量
export CUDA_HOME=/usr/local/cuda
export PATH=$PATH:$CUDA_HOME/bin
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$CUDA_HOME/lib64

# GPU可见性设置
export CUDA_VISIBLE_DEVICES=0
export NVIDIA_VISIBLE_DEVICES=all
```

重新加载配置：
```bash
source ~/.bashrc
```

#### 安装OpenCL支持 (可选)
```bash
sudo apt install ocl-icd-opencl-dev clinfo
```

### 3. 验证安装

#### 检查NVIDIA驱动
```bash
nvidia-smi
```
应该显示GPU信息和驱动版本。

#### 检查CUDA
```bash
nvcc --version
```

#### 检查OpenCL
```bash
clinfo
```

## 🚀 Metashape中的GPU配置

### 基本配置
```python
import Metashape

# WSL推荐配置
Metashape.app.gpu_mask = 0xFFFFFFFF  # 启用所有GPU
Metashape.app.cpu_enable = True      # 启用CPU+GPU混合模式
```

### 性能优化设置

#### 1. 混合模式 (推荐)
```python
# 最佳性能配置
Metashape.app.gpu_mask = 0xFFFFFFFF
Metashape.app.cpu_enable = True
```

#### 2. 纯GPU模式
```python
# 纯GPU模式 (适合小项目)
Metashape.app.gpu_mask = 0xFFFFFFFF
Metashape.app.cpu_enable = False
```

#### 3. 纯CPU模式
```python
# 纯CPU模式 (备用方案)
Metashape.app.gpu_mask = 0
Metashape.app.cpu_enable = True
```

## 🔍 诊断工具

### 使用提供的诊断脚本

#### 1. WSL环境检测
```bash
./metashape -r wsl_gpu_setup.py -platform offscreen
```

#### 2. Metashape GPU配置
```bash
./metashape -r wsl_metashape_gpu.py -platform offscreen
```

#### 3. 快速GPU测试
```bash
./metashape -r quick_gpu_test.py -platform offscreen
```

## ⚠️ 常见问题和解决方案

### 问题1: nvidia-smi显示"NVIDIA-SMI has failed"
**解决方案**:
1. 检查Windows端NVIDIA驱动是否支持WSL
2. 重启WSL: `wsl --shutdown` (在Windows PowerShell中)
3. 重新启动WSL

### 问题2: GPU掩码始终为0
**解决方案**:
1. 确认使用WSL2而不是WSL1
2. 检查CUDA环境变量设置
3. 重启Metashape应用

### 问题3: GPU性能不如预期
**解决方案**:
1. 使用混合模式 (CPU + GPU)
2. 检查GPU内存使用情况
3. 适当调整处理参数 (如downscale)

### 问题4: 内存不足错误
**解决方案**:
1. 降低处理精度 (增加downscale值)
2. 分批处理大型项目
3. 监控系统内存使用

## 📊 性能优化建议

### 1. 参数调优
```python
# 大型项目推荐设置
chunk.matchPhotos(downscale=1, generic_preselection=True)
chunk.buildDepthMaps(downscale=2, filter_mode=Metashape.MildFiltering)
chunk.buildModel(surface_type=Metashape.Arbitrary)

# 快速测试设置
chunk.matchPhotos(downscale=2, generic_preselection=True)
chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
```

### 2. 内存管理
- 监控GPU内存使用: `nvidia-smi`
- 适当调整批处理大小
- 使用渐进式处理策略

### 3. WSL特定优化
- 使用混合模式获得最佳性能
- 避免频繁的CPU-GPU数据传输
- 合理设置WSL内存限制

## 🔄 重启和重置

### 重启WSL
```bash
# 在Windows PowerShell中运行
wsl --shutdown
```

### 重置GPU设置
```python
# 在Metashape中重置
Metashape.app.gpu_mask = 0xFFFFFFFF
Metashape.app.cpu_enable = True
```

## 📝 最佳实践总结

1. **始终使用WSL2**
2. **安装支持WSL的NVIDIA驱动**
3. **使用混合模式 (CPU + GPU)**
4. **定期检查GPU状态**
5. **根据项目大小调整参数**
6. **监控系统资源使用**

## 🆘 获取帮助

如果遇到问题，请：
1. 运行诊断脚本收集信息
2. 检查WSL和驱动版本
3. 查看Metashape日志文件
4. 尝试重启WSL和应用

---

*本指南基于WSL2和最新的NVIDIA驱动程序。配置可能因系统环境而异。*
