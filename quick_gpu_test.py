#!/usr/bin/env python3
"""
快速GPU测试脚本
用于快速检查Metashape是否能识别和使用GPU
"""

import Metashape
import time

def quick_gpu_test():
    """快速GPU测试"""
    print("=== 快速GPU测试 ===")
    
    try:
        # 显示当前设置
        print(f"初始GPU掩码: {Metashape.app.gpu_mask}")
        print(f"初始CPU启用: {Metashape.app.cpu_enable}")
        
        # 保存原始设置
        original_gpu_mask = Metashape.app.gpu_mask
        original_cpu_enable = Metashape.app.cpu_enable
        
        # 测试GPU设置
        print("\n--- 测试GPU设置 ---")
        
        # 测试1: 尝试设置GPU掩码
        test_masks = [1, 2, 4, 15, 255, 0xFFFFFFFF]
        successful_masks = []
        
        for mask in test_masks:
            try:
                Metashape.app.gpu_mask = mask
                actual_mask = Metashape.app.gpu_mask
                if actual_mask != 0:
                    print(f"✓ GPU掩码 {mask} -> 实际设置为 {actual_mask}")
                    successful_masks.append(actual_mask)
                else:
                    print(f"❌ GPU掩码 {mask} -> 设置失败 (返回0)")
            except Exception as e:
                print(f"❌ GPU掩码 {mask} -> 异常: {e}")
        
        if successful_masks:
            print(f"\n✓ 成功的GPU掩码: {list(set(successful_masks))}")
            
            # 使用第一个成功的掩码进行测试
            best_mask = successful_masks[0]
            Metashape.app.gpu_mask = best_mask
            Metashape.app.cpu_enable = True
            
            print(f"\n使用GPU掩码 {best_mask} 进行测试...")
            print("GPU状态: 可用 ✓")
            
        else:
            print("\n❌ 没有找到可用的GPU掩码")
            print("GPU状态: 不可用 ❌")
        
        # 恢复原始设置
        Metashape.app.gpu_mask = original_gpu_mask
        Metashape.app.cpu_enable = original_cpu_enable
        
        return len(successful_masks) > 0
        
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Metashape 快速GPU测试")
    print("=" * 30)
    
    gpu_available = quick_gpu_test()
    
    print("\n" + "=" * 30)
    if gpu_available:
        print("结果: GPU 可用 ✓")
        print("\n建议:")
        print("1. 可以在脚本中使用GPU加速")
        print("2. 建议同时启用CPU和GPU获得最佳性能")
        print("3. 使用 Metashape.app.gpu_mask = 0xFFFFFFFF 启用所有GPU")
    else:
        print("结果: GPU 不可用 ❌")
        print("\n可能的原因:")
        print("1. 没有安装GPU驱动")
        print("2. GPU不支持CUDA/OpenCL")
        print("3. Metashape版本不支持当前GPU")
        print("4. 系统配置问题")
        print("\n建议:")
        print("1. 运行 gpu_diagnostic.py 进行详细诊断")
        print("2. 检查GPU驱动安装")
        print("3. 使用CPU模式继续处理")

if __name__ == "__main__":
    main()
