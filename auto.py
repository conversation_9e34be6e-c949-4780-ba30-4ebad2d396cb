import Metashape
import os
import time

def print_step_time(step_name, start_time):
    """打印步骤执行时间"""
    elapsed = time.time() - start_time
    print(f"{step_name}: {elapsed:.2f} seconds")
    return time.time()

def check_gpu_status():
    """检查GPU状态和配置"""
    print("\n=== GPU 检测和配置信息 ===")

    # 检查GPU掩码
    try:
        gpu_mask = Metashape.app.gpu_mask
        print(f"当前GPU掩码: {gpu_mask} (0x{gpu_mask:X})")
        if gpu_mask == 0:
            print("⚠️  GPU掩码为0，表示未启用GPU")
        else:
            print("✓ GPU掩码已设置")
    except Exception as e:
        print(f"❌ 无法获取GPU掩码: {e}")

    # 检查CPU启用状态
    try:
        cpu_enable = Metashape.app.cpu_enable
        print(f"CPU启用状态: {cpu_enable}")
    except Exception as e:
        print(f"❌ 无法获取CPU状态: {e}")

    # 尝试获取GPU设备信息
    try:
        # 检查是否有可用的GPU设备
        print("\n--- GPU设备检测 ---")

        # 尝试设置GPU掩码来检测GPU
        original_gpu_mask = Metashape.app.gpu_mask
        original_cpu_enable = Metashape.app.cpu_enable

        # 测试不同的GPU掩码值
        test_masks = [1, 2, 4, 8, 15, 255, 0xFFFFFFFF]

        for mask in test_masks:
            try:
                Metashape.app.gpu_mask = mask
                print(f"测试GPU掩码 {mask} (0x{mask:X}): 设置成功")
                break
            except Exception as e:
                print(f"测试GPU掩码 {mask} (0x{mask:X}): 失败 - {e}")

        # 恢复原始设置
        Metashape.app.gpu_mask = original_gpu_mask
        Metashape.app.cpu_enable = original_cpu_enable

    except Exception as e:
        print(f"❌ GPU设备检测失败: {e}")

    print("=" * 40)

# 记录总开始时间
total_start_time = time.time()
step_start_time = time.time()

print("=== Metashape 3D重建流程时间统计 ===")

# 首先检查GPU状态
check_gpu_status()

# 指定图片路径
path = "images/"

# 创建Metashape文档对象
print("步骤1: 初始化文档...")
doc = Metashape.Document()
step_start_time = print_step_time("文档初始化完成", step_start_time)

# 添加图片到文档
print("\n步骤2: 添加图片...")
chunk = doc.addChunk()
chunk.addPhotos([path + f for f in os.listdir(path) if f.endswith(".jpg")])
step_start_time = print_step_time("图片添加完成", step_start_time)

# 对齐图片
print("\n步骤3: 图片匹配...")
chunk.matchPhotos(downscale = 1,
                  generic_preselection = True,
                  reference_preselection = False)
step_start_time = print_step_time("图片匹配完成", step_start_time)

print("\n步骤4: 相机对齐...")
chunk.alignCameras()
step_start_time = print_step_time("相机对齐完成", step_start_time)

# 深度图 - 智能GPU配置
print("\n步骤5: 构建深度图...")

# 尝试优化GPU设置
try:
    # 保存原始设置
    original_gpu_mask = Metashape.app.gpu_mask
    original_cpu_enable = Metashape.app.cpu_enable

    # 如果GPU掩码为0，尝试启用GPU
    if Metashape.app.gpu_mask == 0:
        print("检测到GPU掩码为0，尝试启用GPU...")
        Metashape.app.gpu_mask = 0xFFFFFFFF  # 尝试启用所有GPU
        print(f"GPU掩码已设置为: {Metashape.app.gpu_mask}")

    # 确保CPU也启用（混合模式通常性能最好）
    Metashape.app.cpu_enable = True

    print(f"当前配置 - GPU掩码: {Metashape.app.gpu_mask}, CPU启用: {Metashape.app.cpu_enable}")

except Exception as e:
    print(f"GPU配置警告: {e}")

chunk.buildDepthMaps(downscale = 2,
                     filter_mode = Metashape.MildFiltering)
# NoFiltering, MildFiltering, ModerateFiltering, AggressiveFiltering
step_start_time = print_step_time("深度图构建完成", step_start_time)

# 模型创建 - 确保GPU配置
print("\n步骤6: 构建3D模型...")
try:
    # 确保GPU和CPU都启用以获得最佳性能
    if Metashape.app.gpu_mask == 0:
        Metashape.app.gpu_mask = 0xFFFFFFFF
    Metashape.app.cpu_enable = True
    print(f"模型构建配置 - GPU掩码: {Metashape.app.gpu_mask}, CPU启用: {Metashape.app.cpu_enable}")
except Exception as e:
    print(f"GPU配置警告: {e}")

chunk.buildModel(surface_type = Metashape.Arbitrary,
                 interpolation = Metashape.EnabledInterpolation)
step_start_time = print_step_time("3D模型构建完成", step_start_time)

# 纹理创建
print("\n步骤7: 构建UV映射...")
chunk.buildUV(mapping_mode = Metashape.GenericMapping)
step_start_time = print_step_time("UV映射构建完成", step_start_time)

print("\n步骤8: 构建纹理...")
chunk.buildTexture(blending_mode = Metashape.MosaicBlending,
                   texture_size = 4096)
step_start_time = print_step_time("纹理构建完成", step_start_time)

# 导出obj模型
print("\n步骤9: 导出模型...")
if chunk.model:
    chunk.exportModel(path + '/model.obj')
    step_start_time = print_step_time("模型导出完成", step_start_time)
else:
    print("警告: 没有生成模型，跳过导出步骤")

# 总时间统计
total_elapsed_time = time.time() - total_start_time
print("\n" + "="*50)
print("流程完成!")
print(f"总执行时间: {total_elapsed_time:.2f} seconds ({total_elapsed_time/60:.2f} minutes)")
print("="*50)