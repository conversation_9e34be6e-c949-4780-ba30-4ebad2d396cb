#!/usr/bin/env python3
"""
WSL GPU 配置检测和设置脚本
用于检查WSL环境中的GPU配置和使用情况
"""

import subprocess
import os
import sys

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), -1

def check_wsl_version():
    """检查WSL版本"""
    print("=== WSL版本检测 ===")
    
    # 检查是否在WSL环境中
    if not os.path.exists('/proc/version'):
        print("❌ 不在Linux环境中")
        return False
    
    with open('/proc/version', 'r') as f:
        version_info = f.read()
    
    if 'microsoft' in version_info.lower() or 'wsl' in version_info.lower():
        print("✓ 检测到WSL环境")
        print(f"版本信息: {version_info}")
        
        # 检查WSL版本
        if 'WSL2' in version_info or 'microsoft-standard' in version_info:
            print("✓ WSL2 环境")
            return True
        else:
            print("⚠️  WSL1 环境 - GPU支持有限")
            return False
    else:
        print("❌ 不在WSL环境中")
        return False

def check_gpu_drivers():
    """检查GPU驱动"""
    print("\n=== GPU驱动检测 ===")
    
    # 检查NVIDIA驱动
    print("--- NVIDIA驱动检测 ---")
    stdout, stderr, code = run_command("nvidia-smi")
    if code == 0:
        print("✓ NVIDIA驱动已安装:")
        lines = stdout.split('\n')[:10]  # 只显示前10行
        for line in lines:
            if line.strip():
                print(f"  {line}")
        return True
    else:
        print("❌ NVIDIA驱动未安装或不可用")
        print(f"错误: {stderr}")
        
        # 检查是否有NVIDIA GPU
        stdout, stderr, code = run_command("lspci | grep -i nvidia")
        if code == 0:
            print("⚠️  检测到NVIDIA硬件但驱动不可用:")
            print(stdout)
        
        return False

def check_cuda():
    """检查CUDA环境"""
    print("\n=== CUDA环境检测 ===")
    
    # 检查CUDA版本
    stdout, stderr, code = run_command("nvcc --version")
    if code == 0:
        print("✓ CUDA工具包已安装:")
        print(stdout)
    else:
        print("❌ CUDA工具包未安装")
    
    # 检查CUDA库
    cuda_paths = [
        "/usr/local/cuda",
        "/usr/lib/x86_64-linux-gnu",
        "/usr/lib64"
    ]
    
    cuda_found = False
    for path in cuda_paths:
        if os.path.exists(path):
            stdout, stderr, code = run_command(f"find {path} -name '*cuda*' -type f 2>/dev/null | head -5")
            if stdout:
                print(f"✓ 在 {path} 找到CUDA文件:")
                for line in stdout.split('\n')[:3]:
                    print(f"  {line}")
                cuda_found = True
                break
    
    if not cuda_found:
        print("❌ 未找到CUDA库文件")

def check_opencl():
    """检查OpenCL环境"""
    print("\n=== OpenCL环境检测 ===")
    
    # 检查clinfo
    stdout, stderr, code = run_command("clinfo")
    if code == 0:
        print("✓ OpenCL可用:")
        # 只显示平台信息
        lines = stdout.split('\n')
        for i, line in enumerate(lines):
            if 'Platform' in line or 'Device' in line:
                print(f"  {line}")
            if i > 20:  # 限制输出长度
                break
    else:
        print("❌ OpenCL不可用")
        
        # 检查OpenCL库
        stdout, stderr, code = run_command("find /usr -name '*OpenCL*' 2>/dev/null | head -5")
        if stdout:
            print("⚠️  找到OpenCL库文件:")
            for line in stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")

def check_environment_variables():
    """检查环境变量"""
    print("\n=== 环境变量检测 ===")
    
    important_vars = [
        'CUDA_HOME',
        'CUDA_PATH', 
        'LD_LIBRARY_PATH',
        'PATH'
    ]
    
    for var in important_vars:
        value = os.environ.get(var, '')
        if value:
            print(f"✓ {var}: {value}")
        else:
            print(f"❌ {var}: 未设置")

def provide_wsl_gpu_setup_guide():
    """提供WSL GPU设置指南"""
    print("\n" + "="*60)
    print("WSL GPU 设置指南")
    print("="*60)
    
    print("""
1. 确保使用WSL2:
   - 在Windows PowerShell中运行: wsl --set-version <distro> 2
   - 检查版本: wsl -l -v

2. 安装Windows端NVIDIA驱动:
   - 下载并安装最新的NVIDIA驱动 (支持WSL的版本)
   - 不要在WSL内安装NVIDIA驱动

3. 在WSL中安装CUDA工具包:
   sudo apt update
   sudo apt install nvidia-cuda-toolkit

4. 或者安装官方CUDA:
   wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-ubuntu2004.pin
   sudo mv cuda-ubuntu2004.pin /etc/apt/preferences.d/cuda-repository-pin-600
   wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
   sudo dpkg -i cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
   sudo cp /var/cuda-repo-ubuntu2004-11-8-local/cuda-*-keyring.gpg /usr/share/keyrings/
   sudo apt-get update
   sudo apt-get -y install cuda

5. 设置环境变量 (添加到 ~/.bashrc):
   export CUDA_HOME=/usr/local/cuda
   export PATH=$PATH:$CUDA_HOME/bin
   export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$CUDA_HOME/lib64

6. 安装OpenCL (可选):
   sudo apt install ocl-icd-opencl-dev clinfo

7. 重启WSL:
   在Windows PowerShell中: wsl --shutdown
   然后重新启动WSL

8. 验证安装:
   nvidia-smi
   nvcc --version
   clinfo
""")

def main():
    """主函数"""
    print("WSL GPU 配置检测工具")
    print("="*50)
    
    # 检查WSL版本
    is_wsl2 = check_wsl_version()
    
    if not is_wsl2:
        print("\n⚠️  建议使用WSL2以获得更好的GPU支持")
    
    # 检查GPU驱动
    has_nvidia = check_gpu_drivers()
    
    # 检查CUDA
    check_cuda()
    
    # 检查OpenCL
    check_opencl()
    
    # 检查环境变量
    check_environment_variables()
    
    # 提供设置指南
    if not has_nvidia:
        provide_wsl_gpu_setup_guide()
    
    print("\n" + "="*50)
    print("检测完成")
    
    # 总结建议
    print("\n=== 总结和建议 ===")
    if has_nvidia:
        print("✓ GPU环境配置良好，可以使用GPU加速")
        print("建议:")
        print("1. 在Metashape中设置 gpu_mask = 0xFFFFFFFF")
        print("2. 同时启用CPU和GPU以获得最佳性能")
    else:
        print("❌ GPU环境需要配置")
        print("建议:")
        print("1. 按照上述指南安装GPU驱动和CUDA")
        print("2. 确保使用WSL2")
        print("3. 重启WSL后重新测试")

if __name__ == "__main__":
    main()
