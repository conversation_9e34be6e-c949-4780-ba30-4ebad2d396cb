#!/usr/bin/env python3
"""
Metashape GPU 诊断脚本
用于检测和诊断GPU配置问题
"""

import Metashape
import os
import sys
import subprocess
import time

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), -1

def check_system_gpu():
    """检查系统GPU信息"""
    print("=== 系统GPU信息检测 ===")
    
    # 检查NVIDIA GPU
    print("\n--- NVIDIA GPU 检测 ---")
    stdout, stderr, code = run_command("nvidia-smi")
    if code == 0:
        print("✓ 检测到NVIDIA GPU:")
        print(stdout)
    else:
        print("❌ 未检测到NVIDIA GPU或nvidia-smi不可用")
        print(f"错误: {stderr}")
    
    # 检查AMD GPU
    print("\n--- AMD GPU 检测 ---")
    stdout, stderr, code = run_command("lspci | grep -i vga")
    if code == 0:
        print("✓ 系统显卡信息:")
        print(stdout)
    else:
        print("❌ 无法获取显卡信息")
    
    # 检查OpenCL
    print("\n--- OpenCL 检测 ---")
    stdout, stderr, code = run_command("clinfo")
    if code == 0:
        print("✓ OpenCL可用:")
        print(stdout[:500] + "..." if len(stdout) > 500 else stdout)
    else:
        print("❌ OpenCL不可用或clinfo未安装")
        print(f"错误: {stderr}")

def check_metashape_gpu():
    """检查Metashape GPU配置"""
    print("\n=== Metashape GPU配置检测 ===")
    
    try:
        # 获取当前GPU设置
        print(f"当前GPU掩码: {Metashape.app.gpu_mask} (0x{Metashape.app.gpu_mask:X})")
        print(f"CPU启用状态: {Metashape.app.cpu_enable}")
        
        # 保存原始设置
        original_gpu_mask = Metashape.app.gpu_mask
        original_cpu_enable = Metashape.app.cpu_enable
        
        print("\n--- GPU掩码测试 ---")
        # 测试不同的GPU掩码
        test_masks = [
            (1, "GPU 0"),
            (2, "GPU 1"), 
            (3, "GPU 0+1"),
            (4, "GPU 2"),
            (15, "GPU 0-3"),
            (255, "GPU 0-7"),
            (0xFFFFFFFF, "所有GPU")
        ]
        
        working_masks = []
        
        for mask, description in test_masks:
            try:
                Metashape.app.gpu_mask = mask
                current_mask = Metashape.app.gpu_mask
                if current_mask == mask:
                    print(f"✓ GPU掩码 {mask} ({description}): 设置成功")
                    working_masks.append((mask, description))
                else:
                    print(f"⚠️  GPU掩码 {mask} ({description}): 设置为 {current_mask}")
            except Exception as e:
                print(f"❌ GPU掩码 {mask} ({description}): 失败 - {e}")
        
        # 恢复原始设置
        Metashape.app.gpu_mask = original_gpu_mask
        Metashape.app.cpu_enable = original_cpu_enable
        
        if working_masks:
            print(f"\n✓ 可用的GPU掩码: {[mask for mask, _ in working_masks]}")
            return working_masks
        else:
            print("\n❌ 没有找到可用的GPU掩码")
            return []
            
    except Exception as e:
        print(f"❌ Metashape GPU检测失败: {e}")
        return []

def test_gpu_performance():
    """测试GPU性能"""
    print("\n=== GPU性能测试 ===")
    
    # 创建测试文档
    doc = Metashape.Document()
    chunk = doc.addChunk()
    
    # 检查是否有测试图片
    test_images_path = "images/"
    if not os.path.exists(test_images_path):
        print("❌ 测试图片目录不存在，跳过性能测试")
        return
    
    image_files = [f for f in os.listdir(test_images_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    if len(image_files) < 2:
        print("❌ 测试图片数量不足，跳过性能测试")
        return
    
    # 只使用前几张图片进行快速测试
    test_images = image_files[:min(5, len(image_files))]
    chunk.addPhotos([os.path.join(test_images_path, f) for f in test_images])
    
    print(f"使用 {len(test_images)} 张图片进行测试")
    
    try:
        # 先进行图片匹配和对齐（这些步骤主要使用CPU）
        print("执行图片匹配...")
        chunk.matchPhotos(downscale=2, generic_preselection=True)
        chunk.alignCameras()
        
        # 测试CPU模式
        print("\n--- CPU模式测试 ---")
        Metashape.app.gpu_mask = 0
        Metashape.app.cpu_enable = True
        
        start_time = time.time()
        chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
        cpu_time = time.time() - start_time
        print(f"CPU深度图构建时间: {cpu_time:.2f} 秒")
        
        # 清除深度图
        chunk.clearDepthMaps()
        
        # 测试GPU模式
        print("\n--- GPU模式测试 ---")
        Metashape.app.cpu_enable = False
        Metashape.app.gpu_mask = 0xFFFFFFFF
        
        start_time = time.time()
        chunk.buildDepthMaps(downscale=4, filter_mode=Metashape.MildFiltering)
        gpu_time = time.time() - start_time
        print(f"GPU深度图构建时间: {gpu_time:.2f} 秒")
        
        # 性能对比
        if gpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"\n性能对比:")
            print(f"CPU时间: {cpu_time:.2f} 秒")
            print(f"GPU时间: {gpu_time:.2f} 秒")
            print(f"GPU加速比: {speedup:.2f}x")
            
            if speedup > 1.1:
                print("✓ GPU加速有效")
            elif speedup > 0.9:
                print("⚠️  GPU加速效果不明显")
            else:
                print("❌ GPU可能比CPU更慢")
        
        # 恢复默认设置
        Metashape.app.cpu_enable = True
        Metashape.app.gpu_mask = 0xFFFFFFFF
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def main():
    """主函数"""
    print("Metashape GPU 诊断工具")
    print("=" * 50)
    
    # 1. 检查系统GPU
    check_system_gpu()
    
    # 2. 检查Metashape GPU配置
    working_masks = check_metashape_gpu()
    
    # 3. 如果有可用GPU，进行性能测试
    if working_masks:
        test_gpu_performance()
    else:
        print("\n❌ 未检测到可用GPU，跳过性能测试")
    
    print("\n" + "=" * 50)
    print("诊断完成")
    
    # 提供建议
    print("\n=== 建议 ===")
    if not working_masks:
        print("1. 检查GPU驱动是否正确安装")
        print("2. 确认Metashape版本支持您的GPU")
        print("3. 检查CUDA/OpenCL是否正确配置")
        print("4. 尝试重启Metashape或系统")
    else:
        print("1. GPU检测正常，可以使用GPU加速")
        print(f"2. 建议使用GPU掩码: {working_masks[0][0]}")
        print("3. 对于大型项目，建议同时启用CPU和GPU")

if __name__ == "__main__":
    main()
